"""
数据中心模块：统一管理所有节点的数据存取逻辑
使用LangGraph Store进行数据持久化
"""

import json
import logging
import uuid
from typing import Any, Dict, Optional

import pandas as pd
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from src.zego_tools import DataQueryParams

logger = logging.getLogger(__name__)

DATA_NAMESPACE = ("data_center", "queried_data")


class DataCenter:
    """数据中心：统一管理queried_data的存取操作，使用LangGraph Store"""

    @staticmethod
    def store_data(
        store: BaseStore,
        config: RunnableConfig,
        data_key: str,
        data: pd.DataFrame,
        error_info: str = "",
        query_params: Optional[Any] = None,
        query_title: Optional[str] = None,
    ) -> None:
        """
        存储数据到LangGraph Store中

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息
            data_key: 数据键，如 "initial", "metric_trend_analysis" 等
            data: 查询到的DataFrame数据
            error_info: 错误信息文本
            query_params: 查询参数对象
            query_title: 查询标题（可选，主要用于initial数据）
        """
        # 获取thread_id作为命名空间的一部分，清理不合法字符
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        # 清理thread_id：移除句点和其他不合法字符，用下划线替换
        clean_thread_id = thread_id.replace(".", "_").replace("-", "_")
        namespace = DATA_NAMESPACE + (clean_thread_id,)

        # 构建数据结构，将DataFrame转换为可序列化的格式
        data_entry = {
            "data": data.to_dict("records") if not data.empty else [],
            "data_columns": data.columns.tolist() if not data.empty else [],
            "error_info": error_info,
            "query_params": (
                query_params.to_dict() if query_params and hasattr(query_params, "to_dict") else str(query_params)
            ),
        }

        # 如果有查询标题，添加到数据中（主要用于initial数据）
        if query_title:
            data_entry["query_title"] = query_title

        # 存储数据到Store
        store.put(namespace, data_key, data_entry)

        logger.info(
            f"[DataCenter] ✅ 存储数据到Store: {data_key}, 行数: {len(data)} {f'数据标题: {query_title}' if query_title else ''}"
        )

    @staticmethod
    def get_data(store: BaseStore, config: RunnableConfig, data_key: str) -> Optional[Dict]:
        """
        从LangGraph Store中获取数据

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息
            data_key: 数据键

        Returns:
            数据字典或None，包含重建的DataFrame
        """
        # 获取thread_id作为命名空间的一部分，清理不合法字符
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        # 清理thread_id：移除句点和其他不合法字符，用下划线替换
        clean_thread_id = thread_id.replace(".", "_").replace("-", "_")
        namespace = DATA_NAMESPACE + (clean_thread_id,)

        try:
            # 从Store获取数据
            stored_item = store.get(namespace, data_key)
            if not stored_item:
                logger.warning(f"[DataCenter] Store中未找到数据: {data_key}")
                return None

            data_entry = stored_item.value

            # 重建DataFrame
            if data_entry.get("data") and data_entry.get("data_columns"):
                data_df = pd.DataFrame(data_entry["data"], columns=data_entry["data_columns"])
                # 构建返回结构，保持与原有接口兼容
                result = {
                    "data": data_df,
                    "error_info": data_entry.get("error_info", ""),
                    "query_params": data_entry.get("query_params"),
                }
                if "query_title" in data_entry:
                    result["query_title"] = data_entry["query_title"]

                logger.info(f"[DataCenter] 从Store获取数据: {data_key}")
                return result
            else:
                logger.warning(f"[DataCenter] Store中数据格式异常: {data_key}")
                return None

        except Exception as e:
            logger.error(f"[DataCenter] 获取数据时出错: {data_key}, 错误: {e}")
            return None

    @staticmethod
    def remove_data(store: BaseStore, config: RunnableConfig, data_key: str) -> None:
        """
        从LangGraph Store中移除数据

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息
            data_key: 要移除的数据键
        """
        # 获取thread_id作为命名空间的一部分，清理不合法字符
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        # 清理thread_id：移除句点和其他不合法字符，用下划线替换
        clean_thread_id = thread_id.replace(".", "_").replace("-", "_")
        namespace = DATA_NAMESPACE + (clean_thread_id,)

        try:
            # 检查数据是否存在
            stored_item = store.get(namespace, data_key)
            if stored_item:
                # 删除数据（LangGraph Store通常通过put None来删除）
                store.delete(namespace, data_key)
                logger.info(f"[DataCenter] 从Store移除数据: {data_key}")
            else:
                logger.warning(f"[DataCenter] 尝试移除不存在的数据: {data_key}")
        except Exception as e:
            logger.error(f"[DataCenter] 移除数据时出错: {data_key}, 错误: {e}")

    @staticmethod
    def list_available_data(store: BaseStore, config: RunnableConfig) -> list:
        """
        列出所有可用的数据键

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息

        Returns:
            数据键列表
        """
        # 获取thread_id作为命名空间的一部分，清理不合法字符
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        # 清理thread_id：移除句点和其他不合法字符，用下划线替换
        clean_thread_id = thread_id.replace(".", "_").replace("-", "_")
        namespace = DATA_NAMESPACE + (clean_thread_id,)

        try:
            # 搜索所有数据项
            stored_items = store.search(namespace)
            return [item.key for item in stored_items]
        except Exception as e:
            logger.error(f"[DataCenter] 列出数据键时出错: {e}")
            return []

    @staticmethod
    def clear_all_data(store: BaseStore, config: RunnableConfig) -> None:
        """
        清理所有数据

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息
        """
        # 获取thread_id作为命名空间的一部分，清理不合法字符
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        # 清理thread_id：移除句点和其他不合法字符，用下划线替换
        clean_thread_id = thread_id.replace(".", "_").replace("-", "_")
        namespace = DATA_NAMESPACE + (clean_thread_id,)

        try:
            # 获取所有数据键并删除
            stored_items = store.search(namespace)
            for item in stored_items:
                store.delete(namespace, item.key)
            logger.info(f"[DataCenter] 清理所有数据，共删除 {len(stored_items)} 项")
        except Exception as e:
            logger.error(f"[DataCenter] 清理数据时出错: {e}")

    @staticmethod
    def get_data_prompt(
        store: BaseStore, config: RunnableConfig, data_key: str, data_title: Optional[str] = None
    ) -> str:
        """
        获取数据的简洁提示词格式

        Args:
            store: LangGraph Store实例
            config: 运行配置，包含thread_id等信息
            data_key: 数据键
            data_title: 自定义数据标题，如果不提供则使用默认标题

        Returns:
            格式化的数据提示词字符串
        """
        data_entry = DataCenter.get_data(store, config, data_key)
        if not data_entry:
            return f"**{data_title or data_key}**: 无数据"

        data_df = data_entry.get("data")
        error_info = data_entry.get("error_info", "")
        query_title = data_entry.get("query_title", data_title or data_key)
        query_params = data_entry.get("query_params", {})

        if data_df is None or data_df.empty:
            return f"**{query_title}**: 无数据"

        # 生成简洁的数据提示词
        data_prompt = f"<data>\n**{query_title}**:\n"
        if query_params and hasattr(query_params, "to_prompt"):
            data_prompt += f"<数据说明>\n{query_params.to_prompt()}\n</数据说明>\n"
        data_prompt += f"<数据表>\n{data_df.to_markdown(index=False)}\n</数据表>\n"

        # 如果有错误码信息，添加到提示词中
        if error_info and "错误码说明" in error_info:
            data_prompt += f"\n<错误码说明>\n{error_info}\n</错误码说明>\n"
        data_prompt += "</data>"
        return data_prompt
