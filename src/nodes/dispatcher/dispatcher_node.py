import logging
from typing import Dict, Literal

import pandas as pd
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Send

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.data_center import DataCenter

from src.nodes.common.types import DispatcherInfo
from src.nodes.dispatcher.dispatcher_prompt import dispatcher_prompt_template, json_prompt_template
from src.state import State
from src.zego_tools import DataQueryParams, execute_sql_for_llm, generate_sql

logger = logging.getLogger(__name__)


async def zego_dispatcher_node(state: State, config: RunnableConfig, *, store: BaseStore):
    """
    问题分析和调度节点：分析用户问题，识别参数，决定需要启动哪些维度的并行分析任务，同时预先查询所需数据
    """
    logger.info("🤖zego_dispatcher node is working.")
    node_name = "zego_dispatcher"
    state_update = {"messages": []}

    # 构建提示词
    prompt_list = [
        dispatcher_prompt_template.format(
            CURRENT_TIME=get_prompt_timestamp(),
        ),
        json_prompt_template.format(
            json_schema=DispatcherInfo.model_json_schema(),
        ),
    ]
    prompt = "\n".join(prompt_list)

    # 输入消息
    history_messages = state.get("messages", [])
    input_messages = [*history_messages, SystemMessage(content=prompt)]

    # 请求LLM
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
        node_name, input_messages, DispatcherInfo
    )
    state_update.update(create_token_update(input_tokens, output_tokens))

    # 处理响应
    dispatcher_info: DispatcherInfo = response_parsed
    logger.info(
        f"[{node_name}] ✅dispatcher_info = \n{dispatcher_info.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    # 使用统一的消息生成方法
    state_update["messages"].append(dispatcher_info.to_message())

    # 批量执行数据查询
    logger.info(f"[{node_name}] 开始批量查询数据")
    await _batch_query_data(dispatcher_info, node_name, store, config)

    # 返回更新的状态，包含所有必要信息（数据已通过DataCenter存储）
    state_update.update({"dispatcher_info": dispatcher_info})

    return state_update


async def _batch_query_data(
    dispatcher_info: DispatcherInfo, node_name: str, store: BaseStore, config: RunnableConfig
) -> None:
    """
    基于DataQueryParams直接分组查询，无需复杂映射
    """
    import pandas as pd
    from src.common.utils.dimension_utils import get_analysis_info_from_query_params, get_query_title_from_params
    from src.nodes.common.data_center import DataCenter
    from src.zego_tools import generate_sql
    from src.zego_tools.sql_executor import execute_sql_for_llm

    # 按分析类型分组查询参数
    analysis_groups = {}
    for query_param in dispatcher_info.query_params_list:
        analysis_key, analysis_name = get_analysis_info_from_query_params(query_param)
        if analysis_key not in analysis_groups:
            analysis_groups[analysis_key] = []
        analysis_groups[analysis_key].append((query_param, analysis_name))

    # 为每个分析类型执行查询
    for analysis_key, param_list in analysis_groups.items():
        # 使用第一个查询参数（如有多个相同类型的查询）
        query_params, analysis_name = param_list[0]

        try:
            sql = generate_sql(query_params)
            result, error_info_text = await execute_sql_for_llm(sql)

            query_title = get_query_title_from_params(query_params)

            DataCenter.store_data(
                store=store,
                config=config,
                data_key=analysis_key,  # 直接使用analysis_key
                data=result,
                error_info=error_info_text,
                query_params=query_params,
                query_title=query_title,
            )

            logger.info(f"[{node_name}] ✅ {analysis_name} 查询完成: {len(result)} 条记录")
            if "错误码说明" in error_info_text:
                logger.info(f"[{node_name}] ✅ {analysis_name} 包含错误码信息")

        except Exception as e:
            logger.error(f"[{node_name}] ❌ {analysis_name} 查询失败: {e}")

            query_title = get_query_title_from_params(query_params) + "（查询失败）"

            # 存储空数据和错误信息
            DataCenter.store_data(
                store=store,
                config=config,
                data_key=analysis_key,
                data=pd.DataFrame(),
                error_info=f"查询失败: {str(e)}",
                query_params=query_params,
                query_title=query_title,
            )


def dispatcher_router(state: State):
    """
    基于DataQueryParams直接路由，无需active_dimensions
    """
    from langgraph.types import Send
    from src.common.utils.dimension_utils import get_analysis_info_from_query_params, get_node_name_from_analysis_key

    dispatcher_info = state.get("dispatcher_info")
    if not dispatcher_info:
        return "aggregator"

    # 直接从query_params_list获取需要执行的节点
    node_set = set()
    for query_param in dispatcher_info.query_params_list:
        analysis_key, _ = get_analysis_info_from_query_params(query_param)
        node_name = get_node_name_from_analysis_key(analysis_key)
        node_set.add(node_name)

    if not node_set:
        return "aggregator"

    # 返回需要并行执行的节点
    return [Send(node_name, state) for node_name in node_set]
