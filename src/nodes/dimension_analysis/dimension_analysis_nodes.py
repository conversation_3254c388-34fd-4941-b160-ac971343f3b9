from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command

from src.nodes.dimension_analysis.base_dimension_node import BaseDimensionNode
from src.nodes.dimension_analysis.prompts import (
    dimension_trend_prompt_template,
    error_distribution_prompt_template,
    metric_trend_prompt_template,
)
from src.state import State


async def metric_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """指标趋势分析节点"""
    return await BaseDimensionNode(
        prompt_template=metric_trend_prompt_template,
        analysis_key="metric_trend_analysis",
        analysis_name="指标趋势分析",
    ).execute(state, config, store=store)


async def error_distribution_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """错误码分布分析节点"""
    return await BaseDimensionNode(
        prompt_template=error_distribution_prompt_template,
        analysis_key="error_distribution_analysis",
        analysis_name="错误码分布分析",
    ).execute(state, config, store=store)


async def dimension_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """通用维度下钻分析节点 - 处理所有维度的下钻分析"""
    # 直接使用通用的维度分析模板，支持动态维度
    return await BaseDimensionNode(
        prompt_template=dimension_trend_prompt_template,
    ).execute(state, config, store=store)


# 为了保持向后兼容，保留这些节点函数
async def sdk_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """SDK版本趋势分析节点 - 重定向到通用维度分析节点"""
    return await dimension_trend_analysis_node(state, config, store=store)


async def country_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """国家维度趋势分析节点 - 重定向到通用维度分析节点"""
    return await dimension_trend_analysis_node(state, config, store=store)


async def isp_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """运营商趋势分析节点 - 重定向到通用维度分析节点"""
    return await dimension_trend_analysis_node(state, config, store=store)


async def appid_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """客户维度趋势分析节点 - 重定向到通用维度分析节点"""
    return await dimension_trend_analysis_node(state, config, store=store)
